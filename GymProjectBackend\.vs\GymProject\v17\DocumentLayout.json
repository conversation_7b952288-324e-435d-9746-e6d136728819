{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\workoutprogramtemplatemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\workoutprogramtemplatemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\useroperationclaimmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\useroperationclaimmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\usermanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\usermanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\userlicensemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\userlicensemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\userdevicemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\userdevicemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\usercompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\usercompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\unifiedcompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\unifiedcompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\transactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\transactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\townmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\townmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\systemexercisemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\systemexercisemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\remainingdebtmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\remainingdebtmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\qrcodeencryptionmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\qrcodeencryptionmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\profilemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\profilemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\productmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\productmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\paymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\paymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\operationclaimmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\operationclaimmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\memberworkoutprogrammanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\memberworkoutprogrammanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membershiptypemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membershiptypemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membershipmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membershipmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membershipfreezehistorymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membershipfreezehistorymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\licensetransactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\licensetransactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\licensepackagemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\licensepackagemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\filemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\filemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\expensemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\expensemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\exercisecategorymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\exercisecategorymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\entryexithistorymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\entryexithistorymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\debtpaymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\debtpaymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companyexercisemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companyexercisemanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companyadressmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companyadressmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\citymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\citymanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\authmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\authmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\advancedratelimitmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\advancedratelimitmanager.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\gymcontext.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\gymcontext.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\expense.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\expense.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efremainingdebtdal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efremainingdebtdal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\workoutprogramtemplate.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\workoutprogramtemplate.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\workoutprogramexercise.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\workoutprogramexercise.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\workoutprogramday.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\workoutprogramday.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\userlicense.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\userlicense.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\usercompany.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\usercompany.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\transaction.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\transaction.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\town.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\town.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\systemexercise.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\systemexercise.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\remainingdebt.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\remainingdebt.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\product.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\product.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\payment.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\payment.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\memberworkoutprogram.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\memberworkoutprogram.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\membershiptype.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\membershiptype.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\membershipfreezehistory.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\membershipfreezehistory.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\membership.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\membership.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\licensetransaction.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\licensetransaction.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\member.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\member.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efexpensedal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efexpensedal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\licensepackage.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\licensepackage.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\exercisecategory.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\exercisecategory.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\entryexithistory.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\entryexithistory.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\debtpayment.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\debtpayment.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\companyuser.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\companyuser.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\companyexercise.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\companyexercise.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\companyadress.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\companyadress.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\company.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\company.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\concrete\\city.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\concrete\\city.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\constants\\messages.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\constants\\messages.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\eftransactiondal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\eftransactiondal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efuserlicensedal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efuserlicensedal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmembershipfreezehistorydal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmembershipfreezehistorydal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\eflicensetransactiondal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\eflicensetransactiondal.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 149, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "WorkoutProgramTemplateManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\WorkoutProgramTemplateManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\WorkoutProgramTemplateManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\WorkoutProgramTemplateManager.cs*", "RelativeToolTip": "Business\\Concrete\\WorkoutProgramTemplateManager.cs*", "ViewState": "AgIAAEIAAAAAAAAAAAAAAFUAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:39:38.72Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "UserOperationClaimManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserOperationClaimManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UserOperationClaimManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserOperationClaimManager.cs", "RelativeToolTip": "Business\\Concrete\\UserOperationClaimManager.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:39:34.382Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "UserManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UserManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserManager.cs", "RelativeToolTip": "Business\\Concrete\\UserManager.cs", "ViewState": "AgIAAHsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:39:30.585Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UserLicenseManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserLicenseManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UserLicenseManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserLicenseManager.cs*", "RelativeToolTip": "Business\\Concrete\\UserLicenseManager.cs*", "ViewState": "AgIAAGkAAAAAAAAAAAAAAHcAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:39:13.637Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "UserDeviceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserDeviceManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UserDeviceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserDeviceManager.cs", "RelativeToolTip": "Business\\Concrete\\UserDeviceManager.cs", "ViewState": "AgIAACcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:39:11.237Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UserCompanyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserCompanyManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UserCompanyManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserCompanyManager.cs*", "RelativeToolTip": "Business\\Concrete\\UserCompanyManager.cs*", "ViewState": "AgIAADwAAAAAAAAAAAAAAFYAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:39:00.665Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "UnifiedCompanyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UnifiedCompanyManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UnifiedCompanyManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UnifiedCompanyManager.cs", "RelativeToolTip": "Business\\Concrete\\UnifiedCompanyManager.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:58.502Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "TransactionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TransactionManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\TransactionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TransactionManager.cs*", "RelativeToolTip": "Business\\Concrete\\TransactionManager.cs*", "ViewState": "AgIAAFoAAAAAAAAAAAAAAHQAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:44.611Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "TownManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TownManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\TownManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TownManager.cs*", "RelativeToolTip": "Business\\Concrete\\TownManager.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:41.277Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "SystemExerciseManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\SystemExerciseManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\SystemExerciseManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\SystemExerciseManager.cs", "RelativeToolTip": "Business\\Concrete\\SystemExerciseManager.cs", "ViewState": "AgIAACoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:34.303Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "RemainingDebtManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\RemainingDebtManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\RemainingDebtManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\RemainingDebtManager.cs", "RelativeToolTip": "Business\\Concrete\\RemainingDebtManager.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:31.187Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "QrCodeEncryptionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\QrCodeEncryptionManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\QrCodeEncryptionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\QrCodeEncryptionManager.cs", "RelativeToolTip": "Business\\Concrete\\QrCodeEncryptionManager.cs", "ViewState": "AgIAANwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:26.83Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ProfileManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ProfileManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\ProfileManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ProfileManager.cs", "RelativeToolTip": "Business\\Concrete\\ProfileManager.cs", "ViewState": "AgIAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:22.176Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ProductManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ProductManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\ProductManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ProductManager.cs", "RelativeToolTip": "Business\\Concrete\\ProductManager.cs", "ViewState": "AgIAADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:17.375Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "PaymentManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\PaymentManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\PaymentManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\PaymentManager.cs", "RelativeToolTip": "Business\\Concrete\\PaymentManager.cs", "ViewState": "AgIAAHQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:11.807Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "OperationClaimManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\OperationClaimManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\OperationClaimManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\OperationClaimManager.cs*", "RelativeToolTip": "Business\\Concrete\\OperationClaimManager.cs*", "ViewState": "AgIAACEAAAAAAAAAAAAAADMAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:38:02.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "MemberWorkoutProgramManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberWorkoutProgramManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MemberWorkoutProgramManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberWorkoutProgramManager.cs*", "RelativeToolTip": "Business\\Concrete\\MemberWorkoutProgramManager.cs*", "ViewState": "AgIAAK4AAAAAAAAAAAAAAKoAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:37:47.913Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "MembershipTypeManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipTypeManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MembershipTypeManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipTypeManager.cs*", "RelativeToolTip": "Business\\Concrete\\MembershipTypeManager.cs*", "ViewState": "AgIAADkAAAAAAAAAAAAAAFIAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:37:38.943Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "MembershipManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MembershipManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipManager.cs*", "RelativeToolTip": "Business\\Concrete\\MembershipManager.cs*", "ViewState": "AgIAAKgAAAAAAAAAAAAAALgAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:37:19.595Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "MembershipFreezeHistoryManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipFreezeHistoryManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MembershipFreezeHistoryManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MembershipFreezeHistoryManager.cs*", "RelativeToolTip": "Business\\Concrete\\MembershipFreezeHistoryManager.cs*", "ViewState": "AgIAACYAAAAAAAAAAAAAADkAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:37:10.103Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "MemberManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MemberManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeToolTip": "Business\\Concrete\\MemberManager.cs", "ViewState": "AgIAAEMBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:37:02.278Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "LicensePackageManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\LicensePackageManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\LicensePackageManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\LicensePackageManager.cs*", "RelativeToolTip": "Business\\Concrete\\LicensePackageManager.cs*", "ViewState": "AgIAABUAAAAAAAAAAAAAADYAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:36:44.001Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "ExerciseCategoryManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ExerciseCategoryManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\ExerciseCategoryManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ExerciseCategoryManager.cs", "RelativeToolTip": "Business\\Concrete\\ExerciseCategoryManager.cs", "ViewState": "AgIAADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:36:24.435Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "EntryExitHistoryManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\EntryExitHistoryManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\EntryExitHistoryManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\EntryExitHistoryManager.cs", "RelativeToolTip": "Business\\Concrete\\EntryExitHistoryManager.cs", "ViewState": "AgIAACEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:36:21.996Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "DebtPaymentManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\DebtPaymentManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\DebtPaymentManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\DebtPaymentManager.cs", "RelativeToolTip": "Business\\Concrete\\DebtPaymentManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:36:19.023Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "CompanyUserManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyUserManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyUserManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyUserManager.cs*", "RelativeToolTip": "Business\\Concrete\\CompanyUserManager.cs*", "ViewState": "AgIAAIcAAAAAAAAAAAAAAJ8AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:35:59.756Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "ExpenseManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ExpenseManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\ExpenseManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\ExpenseManager.cs*", "RelativeToolTip": "Business\\Concrete\\ExpenseManager.cs*", "ViewState": "AgIAAHsAAAAAAAAAAAAAAF8AAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:33:20.816Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "LicenseTransactionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\LicenseTransactionManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\LicenseTransactionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\LicenseTransactionManager.cs*", "RelativeToolTip": "Business\\Concrete\\LicenseTransactionManager.cs*", "ViewState": "AgIAACoAAAAAAAAAAAAAAEMAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:34:48.823Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "CompanyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyManager.cs*", "RelativeToolTip": "Business\\Concrete\\CompanyManager.cs*", "ViewState": "AgIAACUAAAAAAAAAAAAAAEkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:35:45.413Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "FileManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\FileManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\FileManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\FileManager.cs", "RelativeToolTip": "Business\\Concrete\\FileManager.cs", "ViewState": "AgIAAJwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:34:57.084Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "CompanyExerciseManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyExerciseManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyExerciseManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyExerciseManager.cs", "RelativeToolTip": "Business\\Concrete\\CompanyExerciseManager.cs", "ViewState": "AgIAAI0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:35:37.159Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "CompanyAdressManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyAdressManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyAdressManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyAdressManager.cs*", "RelativeToolTip": "Business\\Concrete\\CompanyAdressManager.cs*", "ViewState": "AgIAACQAAAAAAAAAAAAAAEAAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:35:29.99Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "CityManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CityManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CityManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CityManager.cs*", "RelativeToolTip": "Business\\Concrete\\CityManager.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:35:21.472Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "AdvancedRateLimitManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\AdvancedRateLimitManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\AdvancedRateLimitManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\AdvancedRateLimitManager.cs", "RelativeToolTip": "Business\\Concrete\\AdvancedRateLimitManager.cs", "ViewState": "AgIAAKEBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:34:44.805Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "EfMemberDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ViewState": "AgIAAKwAAAAAAAAAAAAEwMoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T17:00:50.433Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "Transaction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Transaction.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Transaction.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Transaction.cs", "RelativeToolTip": "Entities\\Concrete\\Transaction.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAABoAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:36:55.07Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "UserCompany.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\UserCompany.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\UserCompany.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\UserCompany.cs", "RelativeToolTip": "Entities\\Concrete\\UserCompany.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:36:59.651Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "UserLicense.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\UserLicense.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\UserLicense.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\UserLicense.cs", "RelativeToolTip": "Entities\\Concrete\\UserLicense.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:37:01.78Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "WorkoutProgramDay.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\WorkoutProgramDay.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\WorkoutProgramDay.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\WorkoutProgramDay.cs", "RelativeToolTip": "Entities\\Concrete\\WorkoutProgramDay.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:38:17.88Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "AuthManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\AuthManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\AuthManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\AuthManager.cs", "RelativeToolTip": "Business\\Concrete\\AuthManager.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAhwFkAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T02:15:17.925Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "Town.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Town.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Town.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Town.cs", "RelativeToolTip": "Entities\\Concrete\\Town.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:36:54.448Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "GymContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "ViewState": "AgIAABAAAAAAAAAAAAAkwCMAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T14:05:41.176Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "WorkoutProgramExercise.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\WorkoutProgramExercise.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\WorkoutProgramExercise.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\WorkoutProgramExercise.cs", "RelativeToolTip": "Entities\\Concrete\\WorkoutProgramExercise.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:38:21.647Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "WorkoutProgramTemplate.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\WorkoutProgramTemplate.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\WorkoutProgramTemplate.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\WorkoutProgramTemplate.cs", "RelativeToolTip": "Entities\\Concrete\\WorkoutProgramTemplate.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:38:25.243Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "EfRemainingDebtDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "ViewState": "AgIAAH4AAAAAAAAAAAAMwJYAAABxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:37:16.259Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "Expense.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Expense.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Expense.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Expense.cs", "RelativeToolTip": "Entities\\Concrete\\Expense.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:21:24.864Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "SystemExercise.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\SystemExercise.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\SystemExercise.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\SystemExercise.cs", "RelativeToolTip": "Entities\\Concrete\\SystemExercise.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:36:52.44Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "RemainingDebt.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\RemainingDebt.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\RemainingDebt.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\RemainingDebt.cs", "RelativeToolTip": "Entities\\Concrete\\RemainingDebt.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAABQAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:36:47.257Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Product.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Product.cs", "RelativeToolTip": "Entities\\Concrete\\Product.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:35:45.722Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "Payment.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Payment.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Payment.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Payment.cs", "RelativeToolTip": "Entities\\Concrete\\Payment.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:35:46.182Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "MemberWorkoutProgram.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MemberWorkoutProgram.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\MemberWorkoutProgram.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MemberWorkoutProgram.cs", "RelativeToolTip": "Entities\\Concrete\\MemberWorkoutProgram.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:35:42.123Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "MembershipType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipType.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\MembershipType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipType.cs", "RelativeToolTip": "Entities\\Concrete\\MembershipType.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:35:34.596Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "MembershipFreezeHistory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipFreezeHistory.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\MembershipFreezeHistory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\MembershipFreezeHistory.cs", "RelativeToolTip": "Entities\\Concrete\\MembershipFreezeHistory.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAIwBkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:35:30.453Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "Membership.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Membership.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Membership.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Membership.cs", "RelativeToolTip": "Entities\\Concrete\\Membership.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:34:16.315Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "LicenseTransaction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicenseTransaction.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\LicenseTransaction.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicenseTransaction.cs", "RelativeToolTip": "Entities\\Concrete\\LicenseTransaction.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABAAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:34:05.293Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "Member.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Member.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Member.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Member.cs", "RelativeToolTip": "Entities\\Concrete\\Member.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:34:12.56Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "EfExpenseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "ViewState": "AgIAACQBAAAAAAAAAAAMwDkBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:22:35.574Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "EntryExitHistory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\EntryExitHistory.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\EntryExitHistory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\EntryExitHistory.cs", "RelativeToolTip": "Entities\\Concrete\\EntryExitHistory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:20:13.236Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "CompanyExercise.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyExercise.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\CompanyExercise.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyExercise.cs", "RelativeToolTip": "Entities\\Concrete\\CompanyExercise.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T23:09:40.084Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "CompanyAdress.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyAdress.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\CompanyAdress.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyAdress.cs", "RelativeToolTip": "Entities\\Concrete\\CompanyAdress.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T23:10:12.492Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "Company.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Company.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\Company.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\Company.cs", "RelativeToolTip": "Entities\\Concrete\\Company.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T23:10:13.063Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "DebtPayment.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\DebtPayment.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\DebtPayment.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\DebtPayment.cs", "RelativeToolTip": "Entities\\Concrete\\DebtPayment.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:20:03.981Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "CompanyUser.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyUser.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\CompanyUser.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\CompanyUser.cs", "RelativeToolTip": "Entities\\Concrete\\CompanyUser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:20:02.041Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "ExerciseCategory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\ExerciseCategory.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\ExerciseCategory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\ExerciseCategory.cs", "RelativeToolTip": "Entities\\Concrete\\ExerciseCategory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:21:22.792Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "LicensePackage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicensePackage.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\LicensePackage.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\LicensePackage.cs", "RelativeToolTip": "Entities\\Concrete\\LicensePackage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:33:54.219Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "City.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\City.cs", "RelativeDocumentMoniker": "Entities\\Concrete\\City.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Concrete\\City.cs", "RelativeToolTip": "Entities\\Concrete\\City.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T23:10:14.109Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "Messages.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Constants\\Messages.cs", "RelativeDocumentMoniker": "Business\\Constants\\Messages.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Constants\\Messages.cs", "RelativeToolTip": "Business\\Constants\\Messages.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAkwCYAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T02:15:23.503Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "EfMembershipFreezeHistoryDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMembershipFreezeHistoryDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMembershipFreezeHistoryDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMembershipFreezeHistoryDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMembershipFreezeHistoryDal.cs", "ViewState": "AgIAAEkAAAAAAAAAAAAnwFoAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:36:24.305Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "EfTransactionDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfTransactionDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfTransactionDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfTransactionDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfTransactionDal.cs", "ViewState": "AgIAACMAAAAAAAAAAAAnwDQAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:37:29.75Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "EfUserLicenseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserLicenseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfUserLicenseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserLicenseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfUserLicenseDal.cs", "ViewState": "AgIAANIAAAAAAAAAAAAnwOMAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:37:36.57Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "EfMemberWorkoutProgramDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ViewState": "AgIAAM0AAAAAAAAAAAAnwN4AAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:35:56.889Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "EfLicenseTransactionDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfLicenseTransactionDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfLicenseTransactionDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfLicenseTransactionDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfLicenseTransactionDal.cs", "ViewState": "AgIAAGQAAAAAAAAAAAAgwHYAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T21:34:25.019Z"}]}]}]}